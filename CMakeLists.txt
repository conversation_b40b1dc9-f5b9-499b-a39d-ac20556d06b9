# CMake 最低版本要求
cmake_minimum_required(VERSION 3.16)

# 项目名称和 C++ 标准
project(Chenghao LANGUAGES CXX)
set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 1. 定义你自己的可执行文件
add_executable(app main.cpp)

# 2. 添加 UHEMesh 子目录
# 使用 EXCLUDE_FROM_ALL 可以避免编译 UHEMesh 自带的大量测试和示例项目
add_subdirectory(deps/UHEMesh EXCLUDE_FROM_ALL)

# 3. 将 UHEMesh 链接到你的目标上
# 从CMake日志可知，UHEMesh提供的核心目标是 UHEMesh_core
target_link_libraries(app PRIVATE UHEMesh_core) # <--- 这里是修改的关键点