#include <iostream>

// =======================================================
// 关键修改：正确的头文件应该是 HEMesh.h
// =======================================================
#include <UHEMesh/HEMesh.h>

int main() {
    // 下面的代码都是正确的，因为类型定义都在 HEMesh.h 及其包含的文件中

    // 使用 UHEMesh 定义的类型
    // UHEMesh::V, UHEMesh::E, UHEMesh::P 分别代表 Vertex, Edge, Polygon
    using heminfo = Ubpa::UHEMesh::HEMeshInfo<size_t, 3>;
    using HEMesh = Ubpa::UHEMesh::HEMesh<heminfo>;

    // 1. 创建一个网格对象
    HEMesh mesh;

    // 2. 添加一些顶点
    mesh.AddVertex({0, 0, 0});
    mesh.AddVertex({1, 0, 0});
    mesh.AddVertex({0, 1, 0});

    // 3. 检查并打印顶点数量
    size_t numVertices = mesh.NumVertices();
    std::cout << "Successfully created a mesh with " << numVertices << " vertices." << std::endl;
    std::cout << "Hello UHEMesh!" << std::endl;

    return 0;
}