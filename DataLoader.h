//
// Created by YANG on 25-7-25.
//

#ifndef DATALOADER_H
#define DATALOADER_H

#include <fstream>
#include <sstream>
#include <vector>
#include <UHEMesh/HEMesh.h>
#include "MyDefinition.h"

/**
 * 数据加载器
 * @tparam Traits 用户自定义类型
 */
template<typename Traits=MyDefinition::MyTraits>
class DataLoader {
public:
    using V = Ubpa::HEMeshTraits_V<Traits>;
    using E = Ubpa::HEMeshTraits_E<Traits>;
    using P = Ubpa::HEMeshTraits_P<Traits>;
    using H = Ubpa::HEMeshTraits_H<Traits>;
    using HEMesh = Ubpa::HEMesh<Traits>;


    static  load
};



#endif //DATALOADER_H
