//
// Created by <PERSON><PERSON><PERSON> on 25-7-25.
//

#ifndef MYDEFINITION_H
#define MYDEFINITION_H

#include <UHEMesh/HEMesh.h>
#include <UHEMesh/TVertex.h>
#include <UHEMesh/TEdge.h>
#include <UHEMesh/TPolygon.h>
#include <UHEMesh/THalfEdge.h>

namespace MyDefinition {

    class MyV;
    class MyE;
    class MyH;
    class MyP;

    using MyTraits = Ubpa::HEMeshTraits<MyV, MyE, MyP, MyH>;

    class MyV : public Ubpa::TVertex<MyTraits> {
    public:
        std::array<double, 3> position;
    };

    class MyE : public Ubpa::TEdge<MyTraits> {
    public:
        double Length() const { return length; }

    private:
        double length;
        std::array<MyV*, 2> vertices;
    };
    class MyH : public Ubpa::THalfEdge<MyTraits> {};
    class MyP : public Ubpa::TPolygon<MyTraits> {};
}


#endif //MYDEFINITION_H
